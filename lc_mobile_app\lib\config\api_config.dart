class ApiConfig {
  // Base URL for the FastAPI backend
  static const String baseUrl =
      'http://********:8000'; // Use ******** for Android emulator to access localhost

  // API endpoints
  static const String loginEndpoint = '/users/login';
  static const String registerEndpoint = '/users/register';
  static const String resetPasswordEndpoint = '/users/reset-password';
  static const String userProfileEndpoint = '/users/me';

  // Token key for shared preferences
  static const String tokenKey = 'auth_token';
}
